@echo off
echo Starting local server for the website...
echo.
cd /d "%~dp0"
echo Current directory: %CD%
echo.

REM Try different server options
echo Attempting to start server...

REM Option 1: Try npm run dev
echo Trying npm run dev...
npm run dev
if %errorlevel% equ 0 goto :success

REM Option 2: Try npx vite
echo Trying npx vite...
npx vite --port 3000
if %errorlevel% equ 0 goto :success

REM Option 3: Try serve
echo Trying npx serve...
npx serve dist -p 3000
if %errorlevel% equ 0 goto :success

REM Option 4: Try http-server
echo Trying npx http-server...
npx http-server dist -p 3000
if %errorlevel% equ 0 goto :success

REM Option 5: Try Python
echo Trying Python server...
cd dist
python -m http.server 3000
if %errorlevel% equ 0 goto :success

echo.
echo All server options failed. Please try manually:
echo 1. Open Command Prompt as Administrator
echo 2. Navigate to: %CD%
echo 3. Run: npm run dev
echo.
pause
goto :end

:success
echo Server started successfully!
echo Open your browser to: http://localhost:3000
pause

:end
