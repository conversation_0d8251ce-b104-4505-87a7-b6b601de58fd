import { Layout } from "@/components/Layout";
import { <PERSON>Header } from "@/components/PageHeader";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { 
  FileText, 
  Plus, 
  Wrench, 
  Bug, 
  Shield, 
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  ExternalLink
} from "lucide-react";
import { Link } from "react-router-dom";

/**
 * Release Notes page component showing version history and updates
 * Features detailed changelog, version comparisons, and migration guides
 * Organized by release types with clear categorization of changes
 */
const ReleaseNotes = () => {
  const releases = [
    {
      version: "v2.4.1",
      date: "2024-12-15",
      type: "patch",
      status: "latest",
      title: "Performance Optimizations & Bug Fixes",
      description: "Minor performance improvements and critical bug fixes for enhanced stability.",
      changes: {
        improvements: [
          "Reduced API response time by 15% for strategy queries",
          "Enhanced memory management in neural prediction engine",
          "Optimized database connection pooling"
        ],
        fixes: [
          "Fixed intermittent connection timeouts in real-time data feed",
          "Resolved parameter validation issue in quantum trading module",
          "Corrected timezone handling in performance reports"
        ],
        security: [
          "Updated encryption protocols for data transmission",
          "Enhanced authentication token validation"
        ]
      },
      migration: false,
      breaking: false
    },
    {
      version: "v2.4.0",
      date: "2024-12-01",
      type: "minor",
      status: "stable",
      title: "Enhanced Risk Management & New Features",
      description: "Major update introducing advanced risk management capabilities and new strategy features.",
      changes: {
        features: [
          "New adaptive risk management module",
          "Enhanced portfolio optimization algorithms",
          "Real-time stress testing capabilities",
          "Advanced correlation analysis tools"
        ],
        improvements: [
          "Improved strategy backtesting performance by 40%",
          "Enhanced user interface for strategy configuration",
          "Better error handling and logging throughout the system"
        ],
        fixes: [
          "Fixed edge case in multi-asset strategy calculations",
          "Resolved memory leak in long-running strategy processes",
          "Corrected historical data alignment issues"
        ]
      },
      migration: true,
      breaking: false
    },
    {
      version: "v2.3.2",
      date: "2024-11-15",
      type: "patch",
      status: "stable",
      title: "Security Updates & Compliance",
      description: "Important security updates and regulatory compliance enhancements.",
      changes: {
        security: [
          "Implemented enhanced encryption for strategy data",
          "Added multi-factor authentication support",
          "Strengthened API rate limiting and abuse prevention"
        ],
        compliance: [
          "Updated audit trail functionality for regulatory requirements",
          "Enhanced data retention policies",
          "Improved compliance reporting features"
        ],
        fixes: [
          "Fixed authentication session timeout issues",
          "Resolved data export formatting problems",
          "Corrected calculation precision in certain edge cases"
        ]
      },
      migration: false,
      breaking: false
    },
    {
      version: "v2.3.0",
      date: "2024-10-30",
      type: "minor",
      status: "stable",
      title: "Machine Learning Enhancements",
      description: "Significant improvements to machine learning models and new AI capabilities.",
      changes: {
        features: [
          "New deep learning models for market prediction",
          "Enhanced sentiment analysis from news sources",
          "Improved pattern recognition algorithms",
          "Advanced feature engineering capabilities"
        ],
        improvements: [
          "Increased prediction accuracy by 12% across all models",
          "Reduced model training time by 35%",
          "Enhanced model interpretability and explainability"
        ],
        breaking: [
          "Updated API endpoints for model configuration",
          "Changed parameter structure for neural network strategies"
        ]
      },
      migration: true,
      breaking: true
    }
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "major":
        return <Zap className="w-5 h-5 text-red-400" />;
      case "minor":
        return <Plus className="w-5 h-5 text-blue-400" />;
      case "patch":
        return <Wrench className="w-5 h-5 text-green-400" />;
      default:
        return <FileText className="w-5 h-5 text-gray-400" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      major: "bg-red-500/20 text-red-400 border-red-500/30",
      minor: "bg-blue-500/20 text-blue-400 border-blue-500/30",
      patch: "bg-green-500/20 text-green-400 border-green-500/30"
    };
    
    return (
      <Badge className={variants[type as keyof typeof variants] || "bg-gray-500/20 text-gray-400 border-gray-500/30"}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      latest: "bg-purple-500/20 text-purple-400 border-purple-500/30",
      stable: "bg-green-500/20 text-green-400 border-green-500/30",
      deprecated: "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-500/20 text-gray-400 border-gray-500/30"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const renderChangeSection = (title: string, changes: string[], icon: React.ReactNode, color: string) => {
    if (!changes || changes.length === 0) return null;
    
    return (
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-3">
          {icon}
          <h4 className={`font-semibold ${color}`}>{title}</h4>
        </div>
        <ul className="space-y-2">
          {changes.map((change, index) => (
            <li key={index} className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
              <span className="text-gray-300 text-sm leading-relaxed">{change}</span>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <Layout>
      <PageHeader
        title="Release Notes"
        subtitle="Version History"
        description="Stay up-to-date with the latest features, improvements, and fixes in our AI strategy platform."
        icon={FileText}
      />

      {/* Latest Release Highlight */}
      <section className="py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border-purple-500/30">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                    <Zap className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">Latest Release: {releases[0].version}</h3>
                    <p className="text-gray-300">{releases[0].title}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getTypeBadge(releases[0].type)}
                  {getStatusBadge(releases[0].status)}
                </div>
              </div>
              <p className="text-gray-300 mb-6">{releases[0].description}</p>
              <div className="flex items-center space-x-4">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  <Download className="w-4 h-4 mr-2" />
                  Download Update
                </Button>
                <Button variant="outline" className="border-white/30 text-white hover:bg-white/10">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Documentation
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Release History */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-white/5 backdrop-blur-sm border border-white/10">
              <TabsTrigger value="all" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">All Releases</TabsTrigger>
              <TabsTrigger value="major" className="data-[state=active]:bg-red-500/20 data-[state=active]:text-red-400">Major</TabsTrigger>
              <TabsTrigger value="minor" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">Minor</TabsTrigger>
              <TabsTrigger value="patch" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">Patches</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-8">
              <div className="space-y-8">
                {releases.map((release, index) => (
                  <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          {getTypeIcon(release.type)}
                          <div>
                            <CardTitle className="text-xl text-white">{release.version} - {release.title}</CardTitle>
                            <p className="text-gray-400">Released on {release.date}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getTypeBadge(release.type)}
                          {getStatusBadge(release.status)}
                          {release.breaking && (
                            <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                              Breaking Changes
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-300">{release.description}</p>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                          {renderChangeSection("New Features", release.changes.features, <Plus className="w-4 h-4" />, "text-blue-400")}
                          {renderChangeSection("Improvements", release.changes.improvements, <TrendingUp className="w-4 h-4" />, "text-green-400")}
                          {renderChangeSection("Bug Fixes", release.changes.fixes, <Bug className="w-4 h-4" />, "text-yellow-400")}
                        </div>
                        <div>
                          {renderChangeSection("Security Updates", release.changes.security, <Shield className="w-4 h-4" />, "text-purple-400")}
                          {renderChangeSection("Compliance", release.changes.compliance, <CheckCircle className="w-4 h-4" />, "text-cyan-400")}
                          {renderChangeSection("Breaking Changes", release.changes.breaking, <AlertTriangle className="w-4 h-4" />, "text-red-400")}
                        </div>
                      </div>
                      
                      {release.migration && (
                        <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                          <div className="flex items-center space-x-2 mb-2">
                            <Clock className="w-4 h-4 text-yellow-400" />
                            <span className="text-yellow-400 font-semibold">Migration Required</span>
                          </div>
                          <p className="text-gray-300 text-sm">
                            This release requires migration steps. Please review the migration guide before updating.
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Filtered views */}
            {["major", "minor", "patch"].map((type) => (
              <TabsContent key={type} value={type} className="mt-8">
                <div className="space-y-8">
                  {releases.filter(release => release.type === type).map((release, index) => (
                    <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            {getTypeIcon(release.type)}
                            <div>
                              <CardTitle className="text-xl text-white">{release.version} - {release.title}</CardTitle>
                              <p className="text-gray-400">Released on {release.date}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getTypeBadge(release.type)}
                            {getStatusBadge(release.status)}
                          </div>
                        </div>
                        <p className="text-gray-300">{release.description}</p>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </section>

      {/* Subscription Section */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Stay <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Updated</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Get notified about new releases, security updates, and important announcements.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/contact"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
                Subscribe to Updates
              </Button>
            </Link>
            <Link
              to="/documentation"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button variant="outline" className="border-white/30 text-white hover:bg-white/10 px-8 py-3 rounded-full transition-all duration-300 hover:scale-105">
                View Documentation
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default ReleaseNotes;
