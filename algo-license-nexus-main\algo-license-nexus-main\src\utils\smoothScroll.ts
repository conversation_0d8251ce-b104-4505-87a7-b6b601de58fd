import { useState, useEffect } from 'react';

/**
 * Smooth scroll utilities for cinematic page transitions
 * Provides eased scrolling with customizable timing functions
 */

interface SmoothScrollOptions {
  duration?: number;
  easing?: (t: number) => number;
  offset?: number;
  callback?: () => void;
}

// Easing functions for natural motion
export const easingFunctions = {
  // Cubic bezier equivalent to CSS cubic-bezier(0.16, 1, 0.3, 1)
  easeOutExpo: (t: number): number => {
    return t === 1 ? 1 : 1 - Math.pow(2, -10 * t);
  },

  // Smooth acceleration and deceleration
  easeInOutCubic: (t: number): number => {
    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
  },

  // Gentle bounce effect
  easeOutBack: (t: number): number => {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
  },

  // Cinematic ease
  cinematicEase: (t: number): number => {
    return t < 0.5
      ? 8 * t * t * t * t
      : 1 - Math.pow(-2 * t + 2, 4) / 2;
  }
};

/**
 * Smooth scroll to element with cinematic easing
 */
export const smoothScrollToElement = (
  element: HTMLElement | string,
  options: SmoothScrollOptions = {}
): Promise<void> => {
  return new Promise((resolve) => {
    const {
      duration = 1200,
      easing = easingFunctions.cinematicEase,
      offset = 0,
      callback
    } = options;

    // Get target element
    const targetElement = typeof element === 'string'
      ? document.querySelector(element) as HTMLElement
      : element;

    if (!targetElement) {
      console.warn('Target element not found for smooth scroll');
      resolve();
      return;
    }

    // Calculate target position
    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;

    // Don't animate if already at target
    if (Math.abs(distance) < 1) {
      resolve();
      return;
    }

    let startTime: number | null = null;

    const animateScroll = (currentTime: number) => {
      if (startTime === null) startTime = currentTime;

      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const easedProgress = easing(progress);

      const currentPosition = startPosition + (distance * easedProgress);
      window.scrollTo(0, currentPosition);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        callback?.();
        resolve();
      }
    };

    requestAnimationFrame(animateScroll);
  });
};

/**
 * Smooth scroll to top of page
 */
export const smoothScrollToTop = (options: SmoothScrollOptions = {}): Promise<void> => {
  return new Promise((resolve) => {
    const {
      duration = 1000,
      easing = easingFunctions.easeOutExpo,
      callback
    } = options;

    const startPosition = window.pageYOffset;

    if (startPosition === 0) {
      resolve();
      return;
    }

    let startTime: number | null = null;

    const animateScroll = (currentTime: number) => {
      if (startTime === null) startTime = currentTime;

      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const easedProgress = easing(progress);

      const currentPosition = startPosition * (1 - easedProgress);
      window.scrollTo(0, currentPosition);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        callback?.();
        resolve();
      }
    };

    requestAnimationFrame(animateScroll);
  });
};

/**
 * Add smooth scroll behavior to navigation links
 */
export const initializeSmoothScroll = (): void => {
  // Add smooth scroll to all anchor links
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    const link = target.closest('a[href^="#"]') as HTMLAnchorElement;

    if (link && link.hash) {
      e.preventDefault();

      const targetElement = document.querySelector(link.hash) as HTMLElement;
      if (targetElement) {
        smoothScrollToElement(targetElement, {
          duration: 1200,
          easing: easingFunctions.cinematicEase,
          offset: 80 // Account for fixed header
        });
      }
    }
  });
};

/**
 * Scroll progress indicator
 */
export const useScrollProgress = (): number => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const updateProgress = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = scrollTop / docHeight;
      setProgress(Math.min(Math.max(scrollPercent, 0), 1));
    };

    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          updateProgress();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    updateProgress(); // Initial calculation

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return progress;
};
