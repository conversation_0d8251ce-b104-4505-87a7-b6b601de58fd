import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * ScrollToTop Component
 * Automatically scrolls to top whenever the route changes
 * This ensures all pages open at the top every time
 */
export const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Scroll to top instantly when route changes
    window.scrollTo({
      top: 0,
      behavior: 'instant' // Use instant for immediate scroll on route change
    });
  }, [pathname]);

  return null; // This component doesn't render anything
};

export default ScrollToTop;
