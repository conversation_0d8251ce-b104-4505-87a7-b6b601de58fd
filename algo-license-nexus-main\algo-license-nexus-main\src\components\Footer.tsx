
import { Link } from "react-router-dom";
import { Separator } from "@/components/ui/separator";
import { Brain, Shield, Mail, Phone, MapPin } from "lucide-react";

/**
 * Footer component providing site navigation and company information
 * Features organized links, contact information, and legal compliance
 * Maintains consistent styling with the overall site design
 */
export const Footer = () => {
  return (
    <footer className="glass-strong border-t border-white/10 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-slate-900/50 to-transparent pointer-events-none" />

      <div className="max-w-7xl mx-auto px-6 py-16 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <Link to="/" className="flex items-center space-x-3 mb-8 group">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-purple-500 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-glow group-hover:shadow-glow-purple">
                <Brain className="w-7 h-7 text-white group-hover:animate-pulse" />
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-black text-white group-hover:text-gradient-blue transition-all duration-300">
                  AI Strategy
                </span>
                <span className="text-sm font-medium text-gray-400 group-hover:text-blue-400 transition-colors duration-300 -mt-1">
                  Licensing
                </span>
              </div>
            </Link>
            <p className="text-gray-400 text-lg mb-6 max-w-md">
              Exclusive algorithmic strategies for elite financial institutions.
              Transform your investment approach with proprietary AI technologies.
            </p>
            <div className="flex items-center space-x-2 text-gray-400">
              <Shield className="w-5 h-5" />
              <span className="text-sm">SEC Registered Investment Advisor</span>
            </div>
          </div>

          {/* Navigation Links */}
          <div>
            <h3 className="text-white font-semibold text-lg mb-4">Company</h3>
            <ul className="space-y-3 text-gray-400">
              <li><Link to="/about" className="hover:text-blue-400 transition-colors">About Us</Link></li>
              <li><Link to="/services" className="hover:text-blue-400 transition-colors">Our Services</Link></li>
              <li><Link to="/pricing" className="hover:text-blue-400 transition-colors">Pricing</Link></li>
              <li><Link to="/documentation" className="hover:text-blue-400 transition-colors">Documentation</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-white font-semibold text-lg mb-4">Contact</h3>
            <ul className="space-y-3 text-gray-400">
              <li className="flex items-center space-x-2">
                <Mail className="w-4 h-4" />
                <span className="text-sm"><EMAIL></span>
              </li>
              <li className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span className="text-sm">+****************</span>
              </li>
              <li className="flex items-center space-x-2">
                <MapPin className="w-4 h-4" />
                <span className="text-sm">350 Fifth Avenue, NY</span>
              </li>
              <li><Link to="/contact" className="hover:text-blue-400 transition-colors">Contact Us</Link></li>
            </ul>
          </div>
        </div>

        <Separator className="my-8 bg-white/10" />

        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm">
            © 2024 AI Strategy Licensing. All rights reserved.
          </div>
          <div className="flex space-x-6 text-gray-400 text-sm mt-4 md:mt-0">
            <Link to="/privacy-policy" className="hover:text-blue-400 transition-colors">Privacy Policy</Link>
            <Link to="/terms-of-service" className="hover:text-blue-400 transition-colors">Terms of Service</Link>
            <Link to="/risk-disclosure" className="hover:text-blue-400 transition-colors">Risk Disclosure</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};
