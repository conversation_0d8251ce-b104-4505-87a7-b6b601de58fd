import { Layout } from "@/components/Layout";
import { <PERSON><PERSON>eader } from "@/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  FileText,
  Code,
  Download,
  BookOpen,
  Settings,
  Shield,
  Zap,
  ArrowRight,
  ExternalLink,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { Link } from "react-router-dom";

/**
 * Documentation page providing comprehensive technical documentation
 * Features API documentation, implementation guides, and compliance information
 * Organized by categories with downloadable resources and code examples
 */
const Documentation = () => {
  const documentationSections = [
    {
      id: "getting-started",
      title: "Getting Started",
      icon: BookOpen,
      description: "Quick start guides and initial setup instructions",
      documents: [
        { name: "Quick Start Guide", type: "PDF", size: "2.3 MB", description: "Essential setup steps for new clients" },
        { name: "System Requirements", type: "PDF", size: "1.1 MB", description: "Technical requirements and compatibility" },
        { name: "Installation Checklist", type: "PDF", size: "0.8 MB", description: "Step-by-step installation verification" }
      ]
    },
    {
      id: "api-reference",
      title: "API Reference",
      icon: Code,
      description: "Complete API documentation with code examples",
      documents: [
        { name: "REST API Documentation", type: "HTML", size: "Live", description: "Interactive API documentation" },
        { name: "Python SDK Guide", type: "PDF", size: "4.2 MB", description: "Python integration examples" },
        { name: "Java SDK Guide", type: "PDF", size: "3.8 MB", description: "Java implementation guide" },
        { name: "C++ Integration", type: "PDF", size: "5.1 MB", description: "High-performance C++ integration" }
      ]
    },
    {
      id: "strategy-guides",
      title: "Strategy Implementation",
      icon: Settings,
      description: "Detailed guides for implementing each strategy",
      documents: [
        { name: "Quantum Trading Setup", type: "PDF", size: "6.7 MB", description: "Complete implementation guide" },
        { name: "Neural Prediction Configuration", type: "PDF", size: "5.4 MB", description: "Model configuration and tuning" },
        { name: "Portfolio Optimizer Guide", type: "PDF", size: "4.9 MB", description: "Risk management configuration" },
        { name: "Multi-Asset Integration", type: "PDF", size: "7.2 MB", description: "Cross-asset implementation" }
      ]
    },
    {
      id: "compliance",
      title: "Compliance & Legal",
      icon: Shield,
      description: "Regulatory compliance and legal documentation",
      documents: [
        { name: "Regulatory Compliance Guide", type: "PDF", size: "3.2 MB", description: "Global regulatory requirements" },
        { name: "Risk Disclosure Statement", type: "PDF", size: "1.8 MB", description: "Required risk disclosures" },
        { name: "Audit Trail Documentation", type: "PDF", size: "2.1 MB", description: "Audit and reporting requirements" },
        { name: "Data Privacy Policy", type: "PDF", size: "1.5 MB", description: "Data handling and privacy" }
      ]
    }
  ];

  const codeExamples = [
    {
      title: "Initialize Strategy",
      language: "Python",
      code: `from ai_strategy_sdk import QuantumTrading

# Initialize quantum trading strategy
strategy = QuantumTrading(
    api_key="your_api_key",
    strategy_id="quantum_v2.1",
    risk_level="moderate"
)

# Configure parameters
strategy.configure({
    "max_position_size": 0.05,
    "stop_loss": 0.02,
    "take_profit": 0.06
})

# Start strategy
strategy.start()`
    },
    {
      title: "Real-time Data Feed",
      language: "JavaScript",
      code: `const { AIStrategyClient } = require('@ai-strategy/sdk');

const client = new AIStrategyClient({
  apiKey: process.env.AI_STRATEGY_API_KEY,
  environment: 'production'
});

// Subscribe to real-time signals
client.subscribe('quantum-signals', (signal) => {
  console.log('New signal:', signal);

  if (signal.confidence > 0.85) {
    executeTradeSignal(signal);
  }
});`
    },
    {
      title: "Risk Management",
      language: "Java",
      code: `import com.aistrategy.RiskManager;
import com.aistrategy.Portfolio;

RiskManager riskManager = new RiskManager();
Portfolio portfolio = new Portfolio();

// Set risk parameters
riskManager.setMaxDrawdown(0.15);
riskManager.setVaRLimit(0.02);

// Monitor portfolio risk
if (riskManager.assessRisk(portfolio) > 0.8) {
    portfolio.reduceExposure(0.5);
    logger.warn("High risk detected, reducing exposure");
}`
    }
  ];

  const quickLinks = [
    { title: "API Status", url: "/api-status", icon: Zap, status: "Operational" },
    { title: "Support Portal", url: "/support-portal", icon: Shield, status: "24/7" },
    { title: "Community Forum", url: "/community-forum", icon: BookOpen, status: "Active" },
    { title: "Release Notes", url: "/release-notes", icon: FileText, status: "Updated" }
  ];

  return (
    <Layout>
      <PageHeader
        title="Technical Documentation"
        subtitle="Implementation Guides"
        description="Comprehensive documentation, API references, and implementation guides to help you successfully integrate and deploy our AI strategies."
        icon={FileText}
      />

      {/* Quick Links */}
      <section className="py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {quickLinks.map((link, index) => (
              <Link
                key={index}
                to={link.url}
                onClick={() => {
                  setTimeout(() => {
                    window.scrollTo({ top: 0, behavior: 'instant' });
                  }, 10);
                }}
              >
                <Card className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105 cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <link.icon className="w-8 h-8 text-blue-400 mx-auto mb-3" />
                    <h3 className="text-white font-semibold mb-2">{link.title}</h3>
                    <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
                      {link.status}
                    </Badge>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Documentation Sections */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <Tabs defaultValue="getting-started" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-white/5 backdrop-blur-sm border border-white/10">
              {documentationSections.map((section) => (
                <TabsTrigger
                  key={section.id}
                  value={section.id}
                  className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400 text-xs"
                >
                  <section.icon className="w-4 h-4 mr-2" />
                  {section.title}
                </TabsTrigger>
              ))}
            </TabsList>

            {documentationSections.map((section) => (
              <TabsContent key={section.id} value={section.id} className="mt-8">
                <div className="mb-8">
                  <h2 className="text-3xl font-bold text-white mb-4">{section.title}</h2>
                  <p className="text-gray-300 text-lg">{section.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {section.documents.map((doc, index) => (
                    <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <FileText className="w-8 h-8 text-blue-400" />
                          <Badge variant="outline" className="border-blue-500/30 text-blue-400">
                            {doc.type}
                          </Badge>
                        </div>
                        <CardTitle className="text-lg text-white">{doc.name}</CardTitle>
                        <CardDescription className="text-gray-400">{doc.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500">{doc.size}</span>
                          <Button
                            size="sm"
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                            onClick={() => {
                              // Simulate download
                              alert(`Downloading ${doc.title}...`);
                            }}
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Download
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </section>

      {/* Code Examples */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Code <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Examples</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Get started quickly with these code examples and integration patterns.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {codeExamples.map((example, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg text-white">{example.title}</CardTitle>
                    <Badge variant="outline" className="border-purple-500/30 text-purple-400">
                      {example.language}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="bg-slate-900/80 rounded-lg p-4 overflow-x-auto">
                    <pre className="text-sm text-gray-300">
                      <code>{example.code}</code>
                    </pre>
                  </div>
                  <Button
                    size="sm"
                    className="mt-4 w-full bg-purple-600 hover:bg-purple-700 text-white"
                    onClick={() => {
                      // Simulate opening external example
                      window.open(`https://github.com/algolicense/examples/${example.title.toLowerCase().replace(/\s+/g, '-')}`, '_blank');
                    }}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Full Example
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Support Section */}
      <section className="py-24 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Need <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Help?</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Our technical support team is here to help you with implementation and troubleshooting.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <Card className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
              <CardContent className="p-8 text-center">
                <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-4">Technical Support</h3>
                <p className="text-gray-400 mb-6">Get help with implementation, configuration, and troubleshooting.</p>
                <Link
                  to="/contact"
                  onClick={() => {
                    setTimeout(() => {
                      window.scrollTo({ top: 0, behavior: 'instant' });
                    }, 10);
                  }}
                >
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    Contact Support
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
              <CardContent className="p-8 text-center">
                <AlertTriangle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-4">Report Issues</h3>
                <p className="text-gray-400 mb-6">Found a bug or have a feature request? Let us know.</p>
                <Button
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                  onClick={() => {
                    // Simulate opening issue reporting system
                    window.open('https://github.com/algolicense/support/issues/new', '_blank');
                  }}
                >
                  Report Issue
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="bg-blue-500/10 border border-blue-500/20 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-white mb-4">Enterprise Support</h3>
            <p className="text-gray-300 mb-6">
              Sovereign tier clients receive dedicated technical account management and priority support.
            </p>
            <Link
              to="/contact"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
                Learn About Enterprise Support
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Documentation;
