import { ReactNode } from "react";
import { LucideIcon } from "lucide-react";
import { useParallax } from "@/hooks/useScrollAnimation";

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  icon?: LucideIcon;
  children?: ReactNode;
  className?: string;
}

/**
 * Modern PageHeader component with enhanced animations and styling
 * Features improved gradient effects, better typography, and dynamic backgrounds
 * Supports custom content through children prop with modern glassmorphism
 */
export const PageHeader = ({
  title,
  subtitle,
  description,
  icon: Icon,
  children,
  className = ""
}: PageHeaderProps) => {
  const parallax1 = useParallax(0.3);
  const parallax2 = useParallax(0.5);
  const parallax3 = useParallax(0.2);

  return (
    <section className={`py-32 px-6 relative overflow-hidden ${className}`}>
      {/* Refined Background Animation with Parallax */}
      <div className="absolute inset-0">
        <div
          ref={parallax1.elementRef}
          className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/12 to-purple-500/12 rounded-full mix-blend-multiply filter blur-3xl animate-float-slow"
          style={parallax1.style}
        ></div>
        <div
          ref={parallax2.elementRef}
          className="absolute top-3/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-drift"
          style={parallax2.style}
        ></div>
        <div
          ref={parallax3.elementRef}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] bg-gradient-to-r from-cyan-500/8 to-blue-500/8 rounded-full mix-blend-multiply filter blur-3xl animate-breathe"
          style={parallax3.style}
        ></div>
      </div>

      <div className="max-w-7xl mx-auto text-center relative z-10">
        {Icon && (
          <div className="flex justify-center mb-12 animate-slide-up">
            <div className="glass-strong px-6 py-3 rounded-full border border-white/20 shadow-glow">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl">
                  <Icon className="w-5 h-5 text-white" />
                </div>
                {subtitle && (
                  <span className="text-sm font-semibold text-white">
                    {subtitle}
                  </span>
                )}
              </div>
            </div>
          </div>
        )}

        <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-white mb-8 leading-none animate-slide-up">
          {title.includes(' ') ? (
            <>
              {title.split(' ').slice(0, -1).join(' ')}{' '}
              <span className="block text-gradient-blue animate-glow">
                {title.split(' ').slice(-1)}
              </span>
            </>
          ) : (
            <span className="text-gradient-blue animate-glow">
              {title}
            </span>
          )}
        </h1>

        {description && (
          <p className="text-xl md:text-2xl lg:text-3xl text-gray-300 mb-12 max-w-5xl mx-auto leading-relaxed font-light animate-slide-up">
            {description}
          </p>
        )}

        <div className="animate-fade-in">
          {children}
        </div>
      </div>
    </section>
  );
};
