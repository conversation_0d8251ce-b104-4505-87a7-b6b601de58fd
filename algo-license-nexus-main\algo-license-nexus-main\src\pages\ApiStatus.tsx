import { useState, useEffect } from "react";
import { Layout } from "@/components/Layout";
import { PageHeader } from "@/components/PageHeader";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Zap, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Clock, 
  Activity,
  Server,
  Database,
  Wifi,
  Shield,
  RefreshCw
} from "lucide-react";
import { Link } from "react-router-dom";

/**
 * API Status page component showing real-time service status
 * Features service health monitoring, uptime statistics, and incident reports
 * Provides transparency into system performance and availability
 */
const ApiStatus = () => {
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdated(new Date());
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const services = [
    {
      name: "Core API",
      status: "operational",
      uptime: "99.98%",
      responseTime: "45ms",
      description: "Main API endpoints for strategy access and management",
      icon: Server
    },
    {
      name: "Real-time Data Feed",
      status: "operational", 
      uptime: "99.95%",
      responseTime: "12ms",
      description: "Live market data and signal streaming",
      icon: Activity
    },
    {
      name: "Authentication Service",
      status: "operational",
      uptime: "99.99%",
      responseTime: "23ms",
      description: "User authentication and authorization",
      icon: Shield
    },
    {
      name: "Strategy Engine",
      status: "operational",
      uptime: "99.97%",
      responseTime: "78ms",
      description: "AI strategy computation and signal generation",
      icon: Zap
    },
    {
      name: "Database Cluster",
      status: "operational",
      uptime: "99.96%",
      responseTime: "15ms",
      description: "Primary data storage and retrieval systems",
      icon: Database
    },
    {
      name: "CDN Network",
      status: "degraded",
      uptime: "98.12%",
      responseTime: "156ms",
      description: "Content delivery and static asset serving",
      icon: Wifi
    }
  ];

  const incidents = [
    {
      id: 1,
      title: "CDN Performance Degradation",
      status: "investigating",
      severity: "minor",
      startTime: "2024-12-15 14:30 UTC",
      description: "Some users may experience slower loading times for documentation and static assets.",
      updates: [
        {
          time: "2024-12-15 15:15 UTC",
          message: "Engineering team has identified the issue and is implementing a fix."
        },
        {
          time: "2024-12-15 14:45 UTC", 
          message: "We are investigating reports of slower CDN performance in the US East region."
        }
      ]
    }
  ];

  const metrics = [
    {
      label: "Overall Uptime",
      value: "99.94%",
      period: "Last 30 days",
      color: "text-green-400"
    },
    {
      label: "Average Response Time",
      value: "42ms",
      period: "Last 24 hours",
      color: "text-blue-400"
    },
    {
      label: "Active Incidents",
      value: "1",
      period: "Current",
      color: "text-yellow-400"
    },
    {
      label: "Resolved This Month",
      value: "3",
      period: "December 2024",
      color: "text-purple-400"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "operational":
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case "degraded":
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case "outage":
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      operational: "bg-green-500/20 text-green-400 border-green-500/30",
      degraded: "bg-yellow-500/20 text-yellow-400 border-yellow-500/30",
      outage: "bg-red-500/20 text-red-400 border-red-500/30"
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-500/20 text-gray-400 border-gray-500/30"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <Layout>
      <PageHeader
        title="API Status"
        subtitle="System Health"
        description="Real-time status and performance metrics for all AI Strategy Licensing services and APIs."
        icon={Zap}
      />

      {/* Status Overview */}
      <section className="py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <CheckCircle className="w-8 h-8 text-green-400" />
              <div>
                <h2 className="text-2xl font-bold text-white">All Systems Operational</h2>
                <p className="text-gray-400">Last updated: {lastUpdated.toLocaleString()}</p>
              </div>
            </div>
            <Button
              onClick={() => setLastUpdated(new Date())}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>

          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {metrics.map((metric, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardContent className="p-6 text-center">
                  <div className={`text-3xl font-bold mb-2 ${metric.color}`}>
                    {metric.value}
                  </div>
                  <div className="text-white font-medium mb-1">{metric.label}</div>
                  <div className="text-gray-400 text-sm">{metric.period}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Service Status */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-white mb-8">Service Status</h2>
          
          <div className="space-y-4">
            {services.map((service, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <service.icon className="w-8 h-8 text-blue-400" />
                      <div>
                        <h3 className="text-lg font-semibold text-white">{service.name}</h3>
                        <p className="text-gray-400 text-sm">{service.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="text-sm text-gray-400">Uptime</div>
                        <div className="text-white font-semibold">{service.uptime}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-gray-400">Response</div>
                        <div className="text-white font-semibold">{service.responseTime}</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(service.status)}
                        {getStatusBadge(service.status)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Current Incidents */}
      {incidents.length > 0 && (
        <section className="py-24 px-6 bg-slate-900/50">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-8">Current Incidents</h2>
            
            <div className="space-y-6">
              {incidents.map((incident) => (
                <Card key={incident.id} className="bg-yellow-500/10 border-yellow-500/30">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-xl text-white">{incident.title}</CardTitle>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                          {incident.severity}
                        </Badge>
                        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                          {incident.status}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-gray-400">Started: {incident.startTime}</p>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300 mb-6">{incident.description}</p>
                    
                    <div className="space-y-4">
                      <h4 className="text-white font-semibold">Updates:</h4>
                      {incident.updates.map((update, index) => (
                        <div key={index} className="border-l-2 border-blue-500/30 pl-4">
                          <div className="text-sm text-gray-400 mb-1">{update.time}</div>
                          <p className="text-gray-300">{update.message}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Support Section */}
      <section className="py-24 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Need <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Support?</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            If you're experiencing issues not listed here, our technical support team is ready to help.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/support-portal"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
                Open Support Ticket
              </Button>
            </Link>
            <Link
              to="/contact"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button variant="outline" className="border-white/30 text-white hover:bg-white/10 px-8 py-3 rounded-full transition-all duration-300 hover:scale-105">
                Contact Support
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default ApiStatus;
