import { useState } from "react";
import { Layout } from "@/components/Layout";
import { PageHeader } from "@/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  CreditCard, 
  Shield, 
  Check, 
  ArrowLeft,
  Lock,
  Calendar,
  User,
  Building,
  Mail,
  Phone
} from "lucide-react";
import { Link } from "react-router-dom";

const Checkout = () => {
  const [selectedPlan, setSelectedPlan] = useState("elite");
  const [isProcessing, setIsProcessing] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  const plans = {
    select: {
      name: "Select License",
      price: "$50,000",
      period: "per year",
      color: "from-blue-600 to-blue-700",
      features: ["Core AI Strategies", "Basic Support", "Quarterly Updates"]
    },
    elite: {
      name: "Elite License", 
      price: "$150,000",
      period: "per year",
      color: "from-purple-600 to-purple-700",
      features: ["Advanced AI Strategies", "Priority Support", "Monthly Updates", "Custom Integration"]
    },
    sovereign: {
      name: "Sovereign License",
      price: "Custom",
      period: "contact for pricing",
      color: "from-yellow-600 to-yellow-700",
      features: ["Exclusive Strategies", "Dedicated Support", "Real-time Updates", "Full Customization"]
    }
  };

  const currentPlan = plans[selectedPlan as keyof typeof plans];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      setIsComplete(true);
    }, 3000);
  };

  if (isComplete) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center px-6">
          <Card className="max-w-2xl w-full bg-white/5 backdrop-blur-sm border-white/10">
            <CardContent className="p-12 text-center">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <Check className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-white mb-4">Order Confirmed!</h1>
              <p className="text-gray-300 mb-8">
                Thank you for your purchase. You'll receive an email with your license details and setup instructions within 24 hours.
              </p>
              <div className="space-y-4">
                <Link to="/">
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                    Return to Homepage
                  </Button>
                </Link>
                <Link to="/contact">
                  <Button variant="outline" className="w-full border-white/30 text-white hover:bg-white/10">
                    Contact Support
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageHeader
        title="Secure Your License"
        description="Complete your order to access our premium AI trading strategies"
        backgroundImage="https://images.unsplash.com/photo-**********-bebda4e38f71?auto=format&fit=crop&w=2000&q=80"
      />

      <section className="py-24 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Order Summary */}
            <div>
              <h2 className="text-2xl font-bold text-white mb-6">Order Summary</h2>
              
              {/* Plan Selection */}
              <Card className="bg-white/5 backdrop-blur-sm border-white/10 mb-6">
                <CardHeader>
                  <CardTitle className="text-white">Selected Plan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(plans).map(([key, plan]) => (
                      <div
                        key={key}
                        className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                          selectedPlan === key
                            ? "border-blue-500 bg-blue-500/10"
                            : "border-white/20 hover:border-white/40"
                        }`}
                        onClick={() => setSelectedPlan(key)}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="text-lg font-semibold text-white">{plan.name}</h3>
                            <p className="text-gray-400">{plan.price} {plan.period}</p>
                          </div>
                          {selectedPlan === key && (
                            <Check className="w-6 h-6 text-blue-400" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Features */}
              <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardHeader>
                  <CardTitle className="text-white">What's Included</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {currentPlan.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <Check className="w-5 h-5 text-green-400" />
                        <span className="text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Payment Form */}
            <div>
              <h2 className="text-2xl font-bold text-white mb-6">Payment Details</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Contact Information */}
                <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <User className="w-5 h-5 mr-2" />
                      Contact Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName" className="text-gray-300">First Name</Label>
                        <Input id="firstName" required className="bg-white/10 border-white/20 text-white" />
                      </div>
                      <div>
                        <Label htmlFor="lastName" className="text-gray-300">Last Name</Label>
                        <Input id="lastName" required className="bg-white/10 border-white/20 text-white" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-gray-300">Email</Label>
                      <Input id="email" type="email" required className="bg-white/10 border-white/20 text-white" />
                    </div>
                    <div>
                      <Label htmlFor="company" className="text-gray-300">Company</Label>
                      <Input id="company" required className="bg-white/10 border-white/20 text-white" />
                    </div>
                  </CardContent>
                </Card>

                {/* Payment Method */}
                <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <CreditCard className="w-5 h-5 mr-2" />
                      Payment Method
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="cardNumber" className="text-gray-300">Card Number</Label>
                      <Input id="cardNumber" placeholder="1234 5678 9012 3456" required className="bg-white/10 border-white/20 text-white" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="expiry" className="text-gray-300">Expiry Date</Label>
                        <Input id="expiry" placeholder="MM/YY" required className="bg-white/10 border-white/20 text-white" />
                      </div>
                      <div>
                        <Label htmlFor="cvv" className="text-gray-300">CVV</Label>
                        <Input id="cvv" placeholder="123" required className="bg-white/10 border-white/20 text-white" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Total */}
                <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-center text-xl font-bold text-white">
                      <span>Total:</span>
                      <span className={`bg-gradient-to-r ${currentPlan.color} bg-clip-text text-transparent`}>
                        {currentPlan.price}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isProcessing}
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 text-lg rounded-xl transition-all duration-300 hover:scale-105 shadow-glow disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Processing Payment...
                    </>
                  ) : (
                    <>
                      <Lock className="w-5 h-5 mr-2" />
                      Complete Secure Purchase
                    </>
                  )}
                </Button>

                <div className="text-center text-sm text-gray-400">
                  <Shield className="w-4 h-4 inline mr-1" />
                  Your payment information is encrypted and secure
                </div>
              </form>

              <div className="mt-6">
                <Link 
                  to="/pricing"
                  onClick={() => {
                    setTimeout(() => {
                      window.scrollTo({ top: 0, behavior: 'instant' });
                    }, 10);
                  }}
                  className="flex items-center text-gray-400 hover:text-white transition-colors"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Pricing
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Checkout;
