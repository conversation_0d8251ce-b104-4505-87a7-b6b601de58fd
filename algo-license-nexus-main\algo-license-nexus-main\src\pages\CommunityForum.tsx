import { Layout } from "@/components/Layout";
import { <PERSON>Header } from "@/components/PageHeader";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Users, 
  MessageSquare, 
  Search, 
  TrendingUp, 
  Clock, 
  Star,
  Pin,
  Eye,
  ThumbsUp,
  Reply,
  Shield,
  Award,
  BookOpen,
  Zap,
  Target,
  HelpCircle
} from "lucide-react";
import { Link } from "react-router-dom";

/**
 * Community Forum page component providing discussion platform
 * Features forum categories, recent posts, and community guidelines
 * Designed for client collaboration and knowledge sharing
 */
const CommunityForum = () => {
  const forumStats = [
    { label: "Active Members", value: "1,247", icon: Users, color: "text-blue-400" },
    { label: "Total Posts", value: "8,932", icon: MessageSquare, color: "text-green-400" },
    { label: "Topics Today", value: "23", icon: TrendingUp, color: "text-purple-400" },
    { label: "Online Now", value: "89", icon: Clock, color: "text-yellow-400" }
  ];

  const categories = [
    {
      id: "general",
      name: "General Discussion",
      description: "General topics about AI trading strategies and market insights",
      icon: MessageSquare,
      color: "text-blue-400",
      posts: 2847,
      topics: 342,
      lastPost: "2 hours ago"
    },
    {
      id: "strategies",
      name: "Strategy Discussion",
      description: "Share experiences and discuss specific trading strategies",
      icon: Target,
      color: "text-green-400",
      posts: 1923,
      topics: 198,
      lastPost: "45 minutes ago"
    },
    {
      id: "technical",
      name: "Technical Support",
      description: "Get help with implementation and technical issues",
      icon: Zap,
      color: "text-yellow-400",
      posts: 1456,
      topics: 287,
      lastPost: "1 hour ago"
    },
    {
      id: "announcements",
      name: "Announcements",
      description: "Official announcements and platform updates",
      icon: Pin,
      color: "text-purple-400",
      posts: 234,
      topics: 45,
      lastPost: "3 days ago"
    },
    {
      id: "research",
      name: "Research & Analysis",
      description: "Share market research and analytical insights",
      icon: BookOpen,
      color: "text-cyan-400",
      posts: 892,
      topics: 156,
      lastPost: "6 hours ago"
    },
    {
      id: "feedback",
      name: "Feature Requests",
      description: "Suggest new features and improvements",
      icon: Star,
      color: "text-orange-400",
      posts: 567,
      topics: 89,
      lastPost: "12 hours ago"
    }
  ];

  const recentPosts = [
    {
      id: 1,
      title: "Best practices for quantum strategy parameter tuning?",
      author: "TradingPro_2024",
      category: "Strategy Discussion",
      replies: 12,
      views: 234,
      lastActivity: "2 hours ago",
      isPinned: false,
      isHot: true,
      authorBadge: "Elite Member"
    },
    {
      id: 2,
      title: "Platform Maintenance Scheduled - December 20th",
      author: "AI_Strategy_Team",
      category: "Announcements",
      replies: 5,
      views: 892,
      lastActivity: "3 days ago",
      isPinned: true,
      isHot: false,
      authorBadge: "Staff"
    },
    {
      id: 3,
      title: "Neural prediction engine showing unexpected results",
      author: "QuantAnalyst",
      category: "Technical Support",
      replies: 8,
      views: 156,
      lastActivity: "45 minutes ago",
      isPinned: false,
      isHot: false,
      authorBadge: "Sovereign Member"
    },
    {
      id: 4,
      title: "Market volatility analysis for Q4 2024",
      author: "MarketResearcher",
      category: "Research & Analysis",
      replies: 15,
      views: 445,
      lastActivity: "6 hours ago",
      isPinned: false,
      isHot: true,
      authorBadge: "Elite Member"
    }
  ];

  const topContributors = [
    { name: "TradingMaster", posts: 342, reputation: 2847, badge: "Sovereign Member" },
    { name: "AIStrategist", posts: 298, reputation: 2156, badge: "Elite Member" },
    { name: "QuantGuru", posts: 267, reputation: 1923, badge: "Elite Member" },
    { name: "MarketWizard", posts: 234, reputation: 1678, badge: "Select Member" }
  ];

  const getBadgeColor = (badge: string) => {
    switch (badge) {
      case "Staff":
        return "bg-red-500/20 text-red-400 border-red-500/30";
      case "Sovereign Member":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Elite Member":
        return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      case "Select Member":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  return (
    <Layout>
      <PageHeader
        title="Community Forum"
        subtitle="Connect & Collaborate"
        description="Join our exclusive community of AI trading professionals. Share insights, get support, and collaborate with fellow strategists."
        icon={Users}
      />

      {/* Access Notice */}
      <section className="py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-blue-500/10 border-blue-500/30">
            <CardContent className="p-8">
              <div className="flex items-center space-x-4">
                <Shield className="w-12 h-12 text-blue-400" />
                <div>
                  <h3 className="text-2xl font-bold text-white mb-2">Exclusive Access Required</h3>
                  <p className="text-gray-300 mb-4">
                    The Community Forum is available exclusively to licensed clients. Please log in with your client credentials to access discussions, post questions, and connect with other professionals.
                  </p>
                  <div className="flex space-x-4">
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                      Client Login
                    </Button>
                    <Link
                      to="/contact"
                      onClick={() => {
                        setTimeout(() => {
                          window.scrollTo({ top: 0, behavior: 'instant' });
                        }, 10);
                      }}
                    >
                      <Button variant="outline" className="border-white/30 text-white hover:bg-white/10">
                        Request Access
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Forum Stats */}
      <section className="py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {forumStats.map((stat, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 text-center">
                <CardContent className="p-6">
                  <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-3`} />
                  <div className={`text-2xl font-bold mb-1 ${stat.color}`}>{stat.value}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Forum Content */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <Tabs defaultValue="categories" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-white/5 backdrop-blur-sm border border-white/10">
              <TabsTrigger value="categories" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
                Categories
              </TabsTrigger>
              <TabsTrigger value="recent" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
                Recent Posts
              </TabsTrigger>
              <TabsTrigger value="contributors" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
                Top Contributors
              </TabsTrigger>
              <TabsTrigger value="guidelines" className="data-[state=active]:bg-yellow-500/20 data-[state=active]:text-yellow-400">
                Guidelines
              </TabsTrigger>
            </TabsList>

            {/* Categories */}
            <TabsContent value="categories" className="mt-8">
              <div className="mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input 
                    placeholder="Search forum categories..."
                    className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {categories.map((category) => (
                  <Card key={category.id} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <category.icon className={`w-8 h-8 ${category.color} flex-shrink-0 mt-1`} />
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-white mb-2">{category.name}</h3>
                          <p className="text-gray-400 text-sm mb-4">{category.description}</p>
                          <div className="flex items-center justify-between text-sm text-gray-500">
                            <span>{category.topics} topics</span>
                            <span>{category.posts} posts</span>
                            <span>Last: {category.lastPost}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Recent Posts */}
            <TabsContent value="recent" className="mt-8">
              <div className="space-y-4">
                {recentPosts.map((post) => (
                  <Card key={post.id} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            {post.isPinned && <Pin className="w-4 h-4 text-yellow-400" />}
                            {post.isHot && <TrendingUp className="w-4 h-4 text-red-400" />}
                            <h3 className="text-lg font-semibold text-white hover:text-blue-400 transition-colors cursor-pointer">
                              {post.title}
                            </h3>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                            <span>by {post.author}</span>
                            <Badge className={getBadgeColor(post.authorBadge)}>
                              {post.authorBadge}
                            </Badge>
                            <span>in {post.category}</span>
                            <span>{post.lastActivity}</span>
                          </div>
                          <div className="flex items-center space-x-6 text-sm text-gray-500">
                            <div className="flex items-center space-x-1">
                              <Reply className="w-4 h-4" />
                              <span>{post.replies} replies</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Eye className="w-4 h-4" />
                              <span>{post.views} views</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Top Contributors */}
            <TabsContent value="contributors" className="mt-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {topContributors.map((contributor, index) => (
                  <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10">
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <Award className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-white">{contributor.name}</h3>
                          <Badge className={getBadgeColor(contributor.badge)}>
                            {contributor.badge}
                          </Badge>
                          <div className="flex items-center space-x-4 text-sm text-gray-400 mt-2">
                            <span>{contributor.posts} posts</span>
                            <span>{contributor.reputation} reputation</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Guidelines */}
            <TabsContent value="guidelines" className="mt-8">
              <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <HelpCircle className="w-6 h-6 mr-2" />
                    Community Guidelines
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3">Code of Conduct</h3>
                    <ul className="space-y-2 text-gray-300">
                      <li>• Maintain professional and respectful communication at all times</li>
                      <li>• Share knowledge and insights to benefit the entire community</li>
                      <li>• Respect confidentiality and proprietary information</li>
                      <li>• Use appropriate categories for your posts and discussions</li>
                      <li>• Search existing topics before creating new ones</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3">Prohibited Content</h3>
                    <ul className="space-y-2 text-gray-300">
                      <li>• Sharing of proprietary strategy code or algorithms</li>
                      <li>• Spam, advertising, or promotional content</li>
                      <li>• Personal attacks or harassment of any kind</li>
                      <li>• Off-topic discussions unrelated to AI trading</li>
                      <li>• Sharing of client-specific performance data</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3">Moderation</h3>
                    <p className="text-gray-300">
                      Our community is moderated by AI Strategy Licensing staff and experienced community members. 
                      Violations of these guidelines may result in warnings, temporary suspension, or permanent removal 
                      from the forum. For questions about moderation decisions, please contact our support team.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Join Community CTA */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Join Our <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Community</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Connect with fellow AI trading professionals and access exclusive discussions, insights, and support.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/pricing"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
                Get Licensed Access
              </Button>
            </Link>
            <Link
              to="/contact"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button variant="outline" className="border-white/30 text-white hover:bg-white/10 px-8 py-3 rounded-full transition-all duration-300 hover:scale-105">
                Contact Sales Team
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default CommunityForum;
