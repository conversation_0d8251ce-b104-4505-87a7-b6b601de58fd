import { useEffect, useCallback, useRef, useState } from 'react';

/**
 * Enhanced performance optimization hooks for better user experience
 * Includes intersection observer, debouncing, throttling, and lazy loading utilities
 * Optimized for 60fps animations and smooth interactions
 */

// Intersection Observer hook for scroll-triggered animations
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const elementRef = useRef<HTMLElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const defaultOptions: IntersectionObserverInit = {
      threshold: 0.1,
      rootMargin: '50px',
      ...options
    };

    observerRef.current = new IntersectionObserver(([entry]) => {
      const isVisible = entry.isIntersecting;
      setIsIntersecting(isVisible);
      
      if (isVisible && !hasIntersected) {
        setHasIntersected(true);
      }
    }, defaultOptions);

    observerRef.current.observe(element);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasIntersected, options]);

  return { elementRef, isIntersecting, hasIntersected };
};

// Debounce hook for performance optimization
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Throttle hook for scroll and resize events
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};

// Lazy loading hook for images and components
export const useLazyLoading = (src: string, placeholder?: string) => {
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const { elementRef, isIntersecting } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '100px'
  });

  useEffect(() => {
    if (isIntersecting && src && !isLoaded) {
      const img = new Image();
      
      img.onload = () => {
        setImageSrc(src);
        setIsLoaded(true);
        setIsError(false);
      };
      
      img.onerror = () => {
        setIsError(true);
        setIsLoaded(false);
      };
      
      img.src = src;
    }
  }, [isIntersecting, src, isLoaded]);

  return { elementRef, imageSrc, isLoaded, isError };
};

// Performance monitoring hook
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    fps: 0,
    memoryUsage: 0,
    loadTime: 0
  });

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        frameCount = 0;
        lastTime = currentTime;
        
        setMetrics(prev => ({ ...prev, fps }));
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };

    // Start FPS monitoring
    measureFPS();

    // Memory usage monitoring (if available)
    if ('memory' in performance) {
      const updateMemory = () => {
        const memory = (performance as any).memory;
        const memoryUsage = Math.round(memory.usedJSHeapSize / 1048576); // MB
        setMetrics(prev => ({ ...prev, memoryUsage }));
      };
      
      const memoryInterval = setInterval(updateMemory, 5000);
      
      return () => {
        cancelAnimationFrame(animationId);
        clearInterval(memoryInterval);
      };
    }

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, []);

  return metrics;
};

// Smooth scroll hook with performance optimization
export const useSmoothScroll = () => {
  const scrollToElement = useCallback((
    elementId: string,
    options: ScrollIntoViewOptions = {}
  ) => {
    const element = document.getElementById(elementId);
    if (!element) return;

    const defaultOptions: ScrollIntoViewOptions = {
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest',
      ...options
    };

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (prefersReducedMotion) {
      defaultOptions.behavior = 'auto';
    }

    element.scrollIntoView(defaultOptions);
  }, []);

  const scrollToTop = useCallback((smooth = true) => {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: (smooth && !prefersReducedMotion) ? 'smooth' : 'auto'
    });
  }, []);

  return { scrollToElement, scrollToTop };
};

// Preload hook for critical resources
export const usePreload = (resources: string[]) => {
  const [loadedResources, setLoadedResources] = useState<Set<string>>(new Set());

  useEffect(() => {
    const preloadResource = (href: string) => {
      return new Promise<void>((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = href;
        
        // Determine resource type
        if (href.match(/\.(woff2?|ttf|otf)$/)) {
          link.as = 'font';
          link.crossOrigin = 'anonymous';
        } else if (href.match(/\.(jpg|jpeg|png|webp|svg)$/)) {
          link.as = 'image';
        } else if (href.match(/\.css$/)) {
          link.as = 'style';
        } else if (href.match(/\.js$/)) {
          link.as = 'script';
        }

        link.onload = () => {
          setLoadedResources(prev => new Set(prev).add(href));
          resolve();
        };
        
        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    Promise.allSettled(resources.map(preloadResource))
      .then(() => {
        console.log('Resource preloading completed');
      })
      .catch(error => {
        console.warn('Some resources failed to preload:', error);
      });
  }, [resources]);

  return { loadedResources };
};

// Animation frame hook for smooth animations
export const useAnimationFrame = (callback: (deltaTime: number) => void, deps: any[] = []) => {
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();
  const callbackRef = useRef(callback);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const animate = useCallback((time: number) => {
    if (previousTimeRef.current !== undefined) {
      const deltaTime = time - previousTimeRef.current;
      callbackRef.current(deltaTime);
    }
    previousTimeRef.current = time;
    requestRef.current = requestAnimationFrame(animate);
  }, []);

  useEffect(() => {
    requestRef.current = requestAnimationFrame(animate);
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, deps);

  const start = useCallback(() => {
    if (!requestRef.current) {
      requestRef.current = requestAnimationFrame(animate);
    }
  }, [animate]);

  const stop = useCallback(() => {
    if (requestRef.current) {
      cancelAnimationFrame(requestRef.current);
      requestRef.current = undefined;
    }
  }, []);

  return { start, stop };
};

// Viewport size hook with throttling
export const useViewportSize = () => {
  const [size, setSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0
  });

  const updateSize = useThrottle(() => {
    setSize({
      width: window.innerWidth,
      height: window.innerHeight
    });
  }, 100);

  useEffect(() => {
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [updateSize]);

  return size;
};
