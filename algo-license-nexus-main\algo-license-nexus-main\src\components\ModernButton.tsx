import { forwardRef, useState, useRef, useEffect } from "react";
import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface ModernButtonProps extends ButtonProps {
  variant?: "primary" | "secondary" | "outline" | "ghost" | "success" | "warning" | "danger";
  size?: "sm" | "md" | "lg" | "xl";
  icon?: LucideIcon;
  iconPosition?: "left" | "right";
  glow?: boolean;
  gradient?: boolean;
  ripple?: boolean;
  loading?: boolean;
  tooltip?: string;
  hapticFeedback?: boolean;
}

/**
 * Enhanced Modern Button component with advanced micro-interactions
 * Features glassmorphism effects, gradients, ripple effects, and haptic feedback
 * Supports icons, multiple variants, loading states, and accessibility features
 * Optimized for conversion-focused design with psychological triggers
 */
export const ModernButton = forwardRef<HTMLButtonElement, ModernButtonProps>(
  ({
    className,
    variant = "primary",
    size = "md",
    icon: Icon,
    iconPosition = "left",
    glow = false,
    gradient = false,
    ripple = true,
    loading = false,
    tooltip,
    hapticFeedback = true,
    children,
    disabled,
    onClick,
    ...props
  }, ref) => {
    const [isPressed, setIsPressed] = useState(false);
    const [ripples, setRipples] = useState<Array<{id: number, x: number, y: number}>>([]);
    const [showTooltip, setShowTooltip] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const rippleId = useRef(0);

    // Enhanced base classes with better performance and accessibility
    const baseClasses = "relative overflow-hidden font-semibold transition-all duration-300 hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 group";

    // Enhanced variant classes with better contrast and conversion optimization
    const variantClasses = {
      primary: gradient
        ? "bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-size-200 bg-pos-0 hover:bg-pos-100 text-white shadow-glow hover:shadow-glow-purple border border-blue-500/30"
        : "bg-blue-600 hover:bg-blue-700 text-white shadow-modern hover:shadow-modern-lg border border-blue-500/30",
      secondary: "glass-strong text-white hover:bg-white/10 border border-white/20 hover:border-white/30 backdrop-blur-sm",
      outline: "border-2 border-blue-500 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-400 bg-transparent",
      ghost: "text-gray-300 hover:text-white hover:bg-white/5 border border-transparent hover:border-white/10",
      success: "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-glow border border-green-500/30",
      warning: "bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white shadow-glow border border-yellow-500/30",
      danger: "bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white shadow-glow border border-red-500/30"
    };

    // Enhanced size classes with better proportions
    const sizeClasses = {
      sm: "px-4 py-2 text-sm rounded-xl min-h-[36px]",
      md: "px-6 py-3 text-base rounded-xl min-h-[44px]",
      lg: "px-8 py-4 text-lg rounded-2xl min-h-[52px]",
      xl: "px-10 py-5 text-xl rounded-2xl min-h-[60px]"
    };

    const glowClasses = glow ? "shadow-glow hover:shadow-glow-purple" : "";

    // Enhanced ripple effect with better performance
    const createRipple = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (!ripple || disabled || loading) return;

      const button = event.currentTarget;
      const rect = button.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const newRipple = {
        id: rippleId.current++,
        x,
        y
      };

      setRipples(prev => [...prev, newRipple]);

      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== newRipple.id));
      }, 600);
    };

    // Enhanced click handler with haptic feedback
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || loading) return;

      setIsPressed(true);
      setTimeout(() => setIsPressed(false), 150);

      // Haptic feedback for supported devices
      if (hapticFeedback && 'vibrate' in navigator) {
        navigator.vibrate(10);
      }

      createRipple(event);
      onClick?.(event);
    };

    // Keyboard accessibility
    const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
      if (event.key === 'Enter' || event.key === ' ') {
        setIsPressed(true);
      }
    };

    const handleKeyUp = (event: React.KeyboardEvent<HTMLButtonElement>) => {
      if (event.key === 'Enter' || event.key === ' ') {
        setIsPressed(false);
      }
    };

    return (
      <div className="relative inline-block">
        <Button
          ref={ref || buttonRef}
          className={cn(
            baseClasses,
            variantClasses[variant],
            sizeClasses[size],
            glowClasses,
            isPressed && "scale-95",
            className
          )}
          disabled={disabled || loading}
          onClick={handleClick}
          onKeyDown={handleKeyDown}
          onKeyUp={handleKeyUp}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          onFocus={() => setShowTooltip(true)}
          onBlur={() => setShowTooltip(false)}
          aria-label={tooltip || (typeof children === 'string' ? children : undefined)}
          {...props}
        >
          {/* Content container with enhanced layout */}
          <div className="relative z-10 flex items-center justify-center space-x-2">
            {loading && (
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white mr-1" />
            )}
            {Icon && iconPosition === "left" && !loading && (
              <Icon className="w-5 h-5 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3" />
            )}
            <span className="transition-all duration-300 group-hover:tracking-wide">
              {children}
            </span>
            {Icon && iconPosition === "right" && !loading && (
              <Icon className="w-5 h-5 transition-transform duration-300 group-hover:scale-110 group-hover:translate-x-0.5" />
            )}
          </div>

          {/* Enhanced hover effect overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-inherit" />

          {/* Ripple effects */}
          {ripples.map((ripple) => (
            <div
              key={ripple.id}
              className="absolute rounded-full bg-white/30 pointer-events-none animate-ping"
              style={{
                left: ripple.x - 10,
                top: ripple.y - 10,
                width: 20,
                height: 20,
                animationDuration: '600ms',
                animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            />
          ))}

          {/* Focus ring for accessibility */}
          <div className="absolute inset-0 rounded-inherit ring-2 ring-blue-500/50 ring-offset-2 ring-offset-transparent opacity-0 group-focus-visible:opacity-100 transition-opacity duration-200" />
        </Button>

        {/* Enhanced tooltip */}
        {tooltip && showTooltip && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg border border-white/10 backdrop-blur-sm z-50 whitespace-nowrap animate-fade-in">
            {tooltip}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900" />
          </div>
        )}
      </div>
    );
  }
);

ModernButton.displayName = "ModernButton";
