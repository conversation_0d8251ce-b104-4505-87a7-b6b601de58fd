import { forwardRef } from "react";
import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface ModernButtonProps extends ButtonProps {
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg" | "xl";
  icon?: LucideIcon;
  iconPosition?: "left" | "right";
  glow?: boolean;
  gradient?: boolean;
}

/**
 * Modern Button component with enhanced styling and animations
 * Features glassmorphism effects, gradients, and smooth interactions
 * Supports icons, multiple variants, and modern visual effects
 */
export const ModernButton = forwardRef<HTMLButtonElement, ModernButtonProps>(
  ({ 
    className, 
    variant = "primary", 
    size = "md", 
    icon: Icon, 
    iconPosition = "left",
    glow = false,
    gradient = false,
    children, 
    ...props 
  }, ref) => {
    const baseClasses = "relative overflow-hidden font-semibold transition-all duration-300 hover:scale-105 active:scale-95";
    
    const variantClasses = {
      primary: gradient 
        ? "bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-size-200 bg-pos-0 hover:bg-pos-100 text-white shadow-glow hover:shadow-glow-purple"
        : "bg-blue-600 hover:bg-blue-700 text-white shadow-modern hover:shadow-modern-lg",
      secondary: "glass-strong text-white hover:bg-white/10 border border-white/20 hover:border-white/30",
      outline: "border-2 border-blue-500 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300",
      ghost: "text-gray-300 hover:text-white hover:bg-white/5"
    };

    const sizeClasses = {
      sm: "px-4 py-2 text-sm rounded-xl",
      md: "px-6 py-3 text-base rounded-xl",
      lg: "px-8 py-4 text-lg rounded-2xl",
      xl: "px-10 py-5 text-xl rounded-2xl"
    };

    const glowClasses = glow ? "shadow-glow hover:shadow-glow-purple" : "";

    return (
      <Button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          glowClasses,
          className
        )}
        {...props}
      >
        <div className="relative z-10 flex items-center justify-center space-x-2">
          {Icon && iconPosition === "left" && (
            <Icon className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" />
          )}
          <span>{children}</span>
          {Icon && iconPosition === "right" && (
            <Icon className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" />
          )}
        </div>
        
        {/* Hover effect overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 hover:opacity-100 transition-opacity duration-300" />
      </Button>
    );
  }
);

ModernButton.displayName = "ModernButton";
