import { useState } from "react";
import { Layout } from "@/components/Layout";
import { PageHeader } from "@/components/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  LifeBuoy, 
  Ticket, 
  Search, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  MessageSquare,
  FileText,
  Users,
  Zap,
  Shield,
  Book
} from "lucide-react";
import { Link } from "react-router-dom";

/**
 * Support Portal page component providing comprehensive support interface
 * Features ticket submission, knowledge base, FAQ, and support resources
 * Organized by support tiers with appropriate access levels
 */
const SupportPortal = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleTicketSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate ticket submission
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
      
      // Reset after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false);
      }, 3000);
    }, 2000);
  };

  const supportTiers = [
    {
      name: "Select Tier",
      responseTime: "48 hours",
      channels: ["Email", "Portal"],
      color: "text-blue-400",
      features: ["Basic support", "Email assistance", "Documentation access"]
    },
    {
      name: "Elite Tier", 
      responseTime: "24 hours",
      channels: ["Email", "Portal", "Phone"],
      color: "text-purple-400",
      features: ["Priority support", "Phone assistance", "Implementation help"]
    },
    {
      name: "Sovereign Tier",
      responseTime: "1 hour",
      channels: ["Email", "Portal", "Phone", "Dedicated"],
      color: "text-yellow-400",
      features: ["24/7 support", "Dedicated manager", "Emergency hotline"]
    }
  ];

  const recentTickets = [
    {
      id: "TKT-2024-001",
      title: "Strategy Performance Question",
      status: "open",
      priority: "medium",
      created: "2024-12-15",
      lastUpdate: "2024-12-15"
    },
    {
      id: "TKT-2024-002", 
      title: "API Integration Issue",
      status: "in-progress",
      priority: "high",
      created: "2024-12-14",
      lastUpdate: "2024-12-15"
    },
    {
      id: "TKT-2024-003",
      title: "Documentation Request",
      status: "resolved",
      priority: "low",
      created: "2024-12-13",
      lastUpdate: "2024-12-14"
    }
  ];

  const faqItems = [
    {
      question: "How do I implement a new strategy?",
      answer: "Strategy implementation involves several steps: 1) Review the strategy documentation, 2) Configure your environment, 3) Test in sandbox mode, 4) Deploy to production. Our implementation team can assist with each step."
    },
    {
      question: "What are the system requirements?",
      answer: "Minimum requirements include: Python 3.8+, 8GB RAM, stable internet connection. For high-frequency strategies, we recommend dedicated servers with low-latency connections."
    },
    {
      question: "How often are strategies updated?",
      answer: "Strategy updates vary by tier: Select tier receives quarterly updates, Elite tier gets monthly updates, and Sovereign tier receives real-time updates as needed."
    },
    {
      question: "Can I customize strategy parameters?",
      answer: "Yes, parameter customization is available based on your tier. Elite and Sovereign tiers have extensive customization options, while Select tier has basic parameter adjustments."
    }
  ];

  const getStatusBadge = (status: string) => {
    const variants = {
      open: "bg-blue-500/20 text-blue-400 border-blue-500/30",
      "in-progress": "bg-yellow-500/20 text-yellow-400 border-yellow-500/30", 
      resolved: "bg-green-500/20 text-green-400 border-green-500/30",
      closed: "bg-gray-500/20 text-gray-400 border-gray-500/30"
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-500/20 text-gray-400 border-gray-500/30"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: "bg-green-500/20 text-green-400 border-green-500/30",
      medium: "bg-yellow-500/20 text-yellow-400 border-yellow-500/30",
      high: "bg-red-500/20 text-red-400 border-red-500/30",
      critical: "bg-red-600/20 text-red-300 border-red-600/30"
    };
    
    return (
      <Badge className={variants[priority as keyof typeof variants] || "bg-gray-500/20 text-gray-400 border-gray-500/30"}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  return (
    <Layout>
      <PageHeader
        title="Support Portal"
        subtitle="Get Help"
        description="Access comprehensive support resources, submit tickets, and get assistance from our expert team."
        icon={LifeBuoy}
      />

      {/* Support Tiers */}
      <section className="py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-2xl font-bold text-white mb-8">Your Support Level</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {supportTiers.map((tier, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <h3 className={`text-xl font-bold mb-2 ${tier.color}`}>{tier.name}</h3>
                  <div className="text-2xl font-bold text-white mb-2">{tier.responseTime}</div>
                  <div className="text-gray-400 text-sm mb-4">Response Time</div>
                  <div className="space-y-2">
                    {tier.features.map((feature, idx) => (
                      <div key={idx} className="text-gray-300 text-sm">{feature}</div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Main Support Interface */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <Tabs defaultValue="tickets" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-white/5 backdrop-blur-sm border border-white/10">
              <TabsTrigger value="tickets" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
                <Ticket className="w-4 h-4 mr-2" />
                Tickets
              </TabsTrigger>
              <TabsTrigger value="new-ticket" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
                <MessageSquare className="w-4 h-4 mr-2" />
                New Ticket
              </TabsTrigger>
              <TabsTrigger value="knowledge" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
                <Book className="w-4 h-4 mr-2" />
                Knowledge Base
              </TabsTrigger>
              <TabsTrigger value="faq" className="data-[state=active]:bg-yellow-500/20 data-[state=active]:text-yellow-400">
                <FileText className="w-4 h-4 mr-2" />
                FAQ
              </TabsTrigger>
            </TabsList>

            {/* Existing Tickets */}
            <TabsContent value="tickets" className="mt-8">
              <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardHeader>
                  <CardTitle className="text-white">Your Support Tickets</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentTickets.map((ticket) => (
                      <div key={ticket.id} className="p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-all duration-300">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-semibold">{ticket.title}</h3>
                          <div className="flex items-center space-x-2">
                            {getPriorityBadge(ticket.priority)}
                            {getStatusBadge(ticket.status)}
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-sm text-gray-400">
                          <span>Ticket ID: {ticket.id}</span>
                          <span>Created: {ticket.created}</span>
                          <span>Last Update: {ticket.lastUpdate}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* New Ticket Form */}
            <TabsContent value="new-ticket" className="mt-8">
              <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardHeader>
                  <CardTitle className="text-white">Submit New Support Ticket</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleTicketSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="category" className="text-gray-300">Category</Label>
                        <Select>
                          <SelectTrigger className="bg-white/10 border-white/20 text-white">
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent className="bg-slate-800 border-white/20">
                            <SelectItem value="technical">Technical Issue</SelectItem>
                            <SelectItem value="implementation">Implementation Help</SelectItem>
                            <SelectItem value="performance">Performance Question</SelectItem>
                            <SelectItem value="billing">Billing Inquiry</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="priority" className="text-gray-300">Priority</Label>
                        <Select>
                          <SelectTrigger className="bg-white/10 border-white/20 text-white">
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent className="bg-slate-800 border-white/20">
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="critical">Critical</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="subject" className="text-gray-300">Subject</Label>
                      <Input 
                        id="subject" 
                        placeholder="Brief description of your issue"
                        className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="description" className="text-gray-300">Description</Label>
                      <Textarea 
                        id="description" 
                        placeholder="Please provide detailed information about your issue, including any error messages, steps to reproduce, and expected behavior..."
                        rows={6}
                        className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    
                    <Button
                      type="submit"
                      disabled={isSubmitting || isSubmitted}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 rounded-xl transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Submitting Ticket...
                        </>
                      ) : isSubmitted ? (
                        <>
                          <CheckCircle className="w-5 h-5 mr-2" />
                          Ticket Submitted Successfully!
                        </>
                      ) : (
                        <>
                          <Ticket className="w-5 h-5 mr-2" />
                          Submit Support Ticket
                        </>
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Knowledge Base */}
            <TabsContent value="knowledge" className="mt-8">
              <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardHeader>
                  <CardTitle className="text-white">Knowledge Base</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="mb-6">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <Input 
                        placeholder="Search knowledge base..."
                        className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {[
                      { title: "Getting Started Guide", category: "Setup", icon: Zap },
                      { title: "API Documentation", category: "Technical", icon: FileText },
                      { title: "Security Best Practices", category: "Security", icon: Shield },
                      { title: "Troubleshooting Common Issues", category: "Support", icon: AlertCircle }
                    ].map((article, index) => (
                      <div key={index} className="p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer">
                        <div className="flex items-center space-x-3 mb-2">
                          <article.icon className="w-6 h-6 text-blue-400" />
                          <div>
                            <h3 className="text-white font-semibold">{article.title}</h3>
                            <p className="text-gray-400 text-sm">{article.category}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* FAQ */}
            <TabsContent value="faq" className="mt-8">
              <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardHeader>
                  <CardTitle className="text-white">Frequently Asked Questions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {faqItems.map((item, index) => (
                      <div key={index} className="p-6 bg-white/5 rounded-lg border border-white/10">
                        <h3 className="text-white font-semibold mb-3">{item.question}</h3>
                        <p className="text-gray-300 leading-relaxed">{item.answer}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Need Immediate <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Help?</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            For urgent issues or if you prefer direct contact, our support team is available through multiple channels.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/contact"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
                Contact Support Team
              </Button>
            </Link>
            <Link
              to="/api-status"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button variant="outline" className="border-white/30 text-white hover:bg-white/10 px-8 py-3 rounded-full transition-all duration-300 hover:scale-105">
                Check System Status
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default SupportPortal;
