import { CinematicReveal } from "@/components/ScrollReveal";
import { useCinematicScroll } from "@/hooks/useScrollAnimation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Spark<PERSON>, Zap, TrendingUp } from "lucide-react";

/**
 * Animation Demo Component
 * Showcases all the new cinematic animation features
 * Use this component to test and demonstrate the animation system
 */
export const AnimationDemo = () => {
  // Cinematic scroll effects
  const backgroundParallax = useCinematicScroll({
    parallaxSpeed: 0.3,
    enableScale: true,
    scaleRange: [1, 1.1],
    enableParallax: true
  });

  const floatingElements = useCinematicScroll({
    parallaxSpeed: 0.6,
    enableScale: true,
    scaleRange: [0.95, 1.05],
    enableRotation: true,
    rotationRange: [-2, 2]
  });

  return (
    <section className="py-24 px-6 relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
      {/* Background Effects */}
      <div ref={backgroundParallax.elementRef} style={backgroundParallax.cinematicStyle} className="absolute inset-0 opacity-30">
        <div className="absolute w-[600px] h-[600px] bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-3xl top-1/4 left-1/4 animate-parallax-float" />
        <div className="absolute w-[400px] h-[400px] bg-gradient-to-r from-cyan-500/15 to-blue-500/15 rounded-full blur-2xl bottom-1/4 right-1/4 animate-scale-breathe" />
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        {/* Header with Cinematic Reveal */}
        <CinematicReveal delay={200} animationType="cinematic">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Cinematic <span className="bg-gradient-to-r from-blue-400 via-cyan-400 to-purple-400 bg-clip-text text-transparent animate-text-shimmer bg-size-200">Animation</span> Demo
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Experience the sophisticated scroll animations inspired by premium websites
            </p>
          </div>
        </CinematicReveal>

        {/* Animation Type Demonstrations */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {/* Cinematic Reveal Demo */}
          <CinematicReveal delay={400} animationType="cinematic">
            <Card className="bg-slate-800/80 backdrop-blur-sm border-white/30 hover:border-white/40 transition-all duration-500 hover:scale-105">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mb-4 animate-scale-breathe">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-white">Cinematic Reveal</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">
                  Sophisticated reveal animation with blur, scale, and rotation effects for premium feel.
                </p>
              </CardContent>
            </Card>
          </CinematicReveal>

          {/* Sequential Fade Demo */}
          <CinematicReveal delay={600} animationType="sequential">
            <Card className="bg-slate-800/80 backdrop-blur-sm border-white/30 hover:border-white/40 transition-all duration-500 hover:scale-105">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4 animate-scale-breathe">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-white">Sequential Fade</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">
                  Coordinated sequential animations that create smooth, flowing transitions.
                </p>
              </CardContent>
            </Card>
          </CinematicReveal>

          {/* Element Choreography Demo */}
          <CinematicReveal delay={800} animationType="choreography">
            <Card className="bg-slate-800/80 backdrop-blur-sm border-white/30 hover:border-white/40 transition-all duration-500 hover:scale-105">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4 animate-scale-breathe">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-white">Element Choreography</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">
                  Complex multi-axis choreographed animations for sophisticated element reveals.
                </p>
              </CardContent>
            </Card>
          </CinematicReveal>
        </div>

        {/* Floating Elements Demo */}
        <CinematicReveal delay={1000} animationType="sequential">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-8">
              Floating Elements with Parallax
            </h3>
            <div ref={floatingElements.elementRef} style={floatingElements.cinematicStyle} className="relative">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                {[
                  { label: "Parallax Speed", value: "0.3x - 0.6x" },
                  { label: "Scale Range", value: "0.95 - 1.1" },
                  { label: "Rotation", value: "-2° to +2°" },
                  { label: "Performance", value: "60fps" }
                ].map((item, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl font-bold text-blue-400 mb-2">{item.value}</div>
                    <div className="text-sm text-gray-300">{item.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CinematicReveal>

        {/* Performance Features */}
        <CinematicReveal delay={1200} animationType="cinematic">
          <div className="mt-16 p-8 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl">
            <h3 className="text-2xl font-bold text-white mb-6 text-center">Performance Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                "Hardware Acceleration",
                "Intersection Observer",
                "60fps Optimization",
                "Reduced Motion Support"
              ].map((feature, index) => (
                <div key={index} className="text-center p-4 bg-white/5 rounded-xl">
                  <div className="text-green-400 font-semibold">{feature}</div>
                </div>
              ))}
            </div>
          </div>
        </CinematicReveal>
      </div>
    </section>
  );
};
