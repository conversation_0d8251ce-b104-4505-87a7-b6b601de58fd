
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star } from "lucide-react";

export const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      title: "Chief Investment Officer",
      company: "Quantum Capital",
      avatar: "MC",
      rating: 5,
      quote: "The quantum trading algorithms have revolutionized our portfolio performance. We've seen a 340% increase in returns within the first quarter.",
      results: "+340% ROI"
    },
    {
      name: "<PERSON>",
      title: "Head of Quantitative Research",
      company: "Pinnacle Investments",
      avatar: "SW",
      rating: 5,
      quote: "The neural prediction engine's accuracy is unprecedented. It's like having a crystal ball for market movements.",
      results: "94% Accuracy"
    },
    {
      name: "<PERSON>",
      title: "Managing Partner",
      company: "Elite Trading Group",
      avatar: "DR",
      rating: 5,
      quote: "These exclusive strategies have given us an unfair advantage in the market. The licensing model ensures we stay ahead of competition.",
      results: "+280% Growth"
    }
  ];

  return (
    <section className="py-24 px-6 bg-gradient-to-r from-slate-900/50 to-blue-900/30">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Client <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Success Stories</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Leading financial institutions trust our AI strategies to drive exceptional results.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105">
              <CardContent className="p-8">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <blockquote className="text-gray-300 text-lg mb-6 italic">
                  "{testimonial.quote}"
                </blockquote>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Avatar className="w-12 h-12 mr-4">
                      <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold">
                        {testimonial.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="text-white font-semibold">{testimonial.name}</div>
                      <div className="text-gray-400 text-sm">{testimonial.title}</div>
                      <div className="text-blue-400 text-sm font-medium">{testimonial.company}</div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-green-400 font-bold text-xl">{testimonial.results}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
