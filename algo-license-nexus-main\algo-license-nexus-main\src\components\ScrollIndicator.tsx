import { useEffect, useState } from "react";
import { ChevronDown } from "lucide-react";

/**
 * Subtle scroll indicator component
 * Shows a gentle animation to encourage scrolling
 * Fades out as user scrolls down the page
 */
export const ScrollIndicator = () => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY;
      setIsVisible(scrolled < 100);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-40 animate-fade-in">
      <div className="flex flex-col items-center space-y-2 animate-bounce">
        <div className="text-xs text-gray-400 font-medium uppercase tracking-wider letter-spacing-wide">
          Scroll to explore
        </div>
        <div className="w-6 h-10 border-2 border-white/20 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/40 rounded-full mt-2 animate-pulse-subtle"></div>
        </div>
        <ChevronDown className="w-4 h-4 text-white/40 animate-pulse-subtle" />
      </div>
    </div>
  );
};
