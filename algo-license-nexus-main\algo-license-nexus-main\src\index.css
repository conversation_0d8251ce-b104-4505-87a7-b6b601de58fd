@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Modern Design System Variables */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --premium-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    scroll-behavior: smooth;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* High Contrast Glass Effects for Better Readability */
  .glass {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-strong {
    background: rgba(15, 23, 42, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 16px 48px rgba(0, 0, 0, 0.7),
      inset 0 1px 0 rgba(255, 255, 255, 0.15),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  .glass-subtle {
    background: rgba(15, 23, 42, 0.92);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.08);
  }

  /* Modern Shadows */
  .shadow-modern {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-modern-lg {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-glow {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.1);
  }

  /* Modern Gradients */
  .gradient-premium {
    background: var(--premium-gradient);
  }

  .gradient-accent {
    background: var(--accent-gradient);
  }

  .gradient-success {
    background: var(--success-gradient);
  }

  /* Modern Typography */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-blue {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Subtle Modern Animations */
  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-float-slow {
    animation: float 12s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 3s ease-in-out infinite alternate;
  }

  .animate-glow-subtle {
    animation: glowSubtle 4s ease-in-out infinite alternate;
  }

  .animate-slide-up {
    animation: slideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1); /* Reduced from 0.8s to 0.4s */
  }

  .animate-fade-in {
    animation: fadeIn 0.5s cubic-bezier(0.16, 1, 0.3, 1); /* Reduced from 1s to 0.5s */
  }

  .animate-scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.16, 1, 0.3, 1); /* Reduced from 0.7s to 0.4s */
  }

  .animate-breathe {
    animation: breathe 4s ease-in-out infinite;
  }

  .animate-pulse-subtle {
    animation: pulseSubtle 4s ease-in-out infinite;
  }

  .animate-drift {
    animation: drift 20s ease-in-out infinite;
  }

  /* Cinematic Animation Classes - Faster Timing */
  .animate-section-reveal {
    animation: sectionReveal 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Reduced from 1.4s to 0.8s */
  }

  .animate-hero-entrance {
    animation: heroEntrance 1.2s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Reduced from 2s to 1.2s */
  }

  .animate-card-cascade {
    animation: cardCascade 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Reduced from 1.2s to 0.8s */
  }

  .animate-text-reveal {
    animation: textReveal 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Reduced from 1s to 0.6s */
  }

  .animate-progressive-blur {
    animation: progressiveBlur 0.9s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Reduced from 1.5s to 0.9s */
  }

  .animate-parallax-slow {
    animation: parallaxSlow 30s linear infinite;
  }

  .animate-parallax-medium {
    animation: parallaxMedium 20s linear infinite;
  }

  .animate-parallax-fast {
    animation: parallaxFast 15s linear infinite;
  }

  /* Enhanced Cinematic Animations - Faster Timing */
  .animate-cinematic-reveal {
    animation: cinematicReveal 1.2s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Reduced from 2s to 1.2s */
  }

  .animate-sequential-fade {
    animation: sequentialFade 1s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Reduced from 1.8s to 1s */
  }

  .animate-parallax-float {
    animation: parallaxFloat 8s ease-in-out infinite; /* Keeping this as is for smooth floating */
  }

  .animate-scale-breathe {
    animation: scaleBreath 6s ease-in-out infinite; /* Keeping this as is for smooth breathing */
  }

  .animate-text-shimmer {
    animation: textShimmer 3s ease-in-out infinite; /* Keeping this as is for smooth shimmer */
  }

  .animate-element-choreography {
    animation: elementChoreography 1.5s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Reduced from 2.5s to 1.5s */
  }

  /* Fast Text Animation Classes */
  .animate-text-fast {
    animation: textReveal 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Fast text reveal */
  }

  .animate-zoom-fast {
    animation: scaleIn 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Fast zoom in */
  }

  .animate-blur-fast {
    animation: progressiveBlur 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Fast blur clear */
  }

  /* Background utilities */
  .bg-size-200 {
    background-size: 200% 200%;
  }

  .bg-pos-0 {
    background-position: 0% 50%;
  }

  .bg-pos-100 {
    background-position: 100% 50%;
  }

  /* Enhanced Micro-Interaction Effects */
  .hover-lift {
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1), box-shadow 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .hover-glow {
    transition: box-shadow 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }

  .hover-scale {
    transition: transform 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  /* Advanced Micro-Interactions */
  .hover-tilt {
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-tilt:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg) scale(1.02);
  }

  .hover-bounce {
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .hover-bounce:hover {
    transform: translateY(-4px) scale(1.05);
  }

  .hover-magnetic {
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-magnetic:hover {
    transform: translate3d(2px, -2px, 0) scale(1.02);
  }

  .hover-shimmer {
    position: relative;
    overflow: hidden;
  }

  .hover-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .hover-shimmer:hover::before {
    left: 100%;
  }

  .hover-glow-pulse {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-glow-pulse:hover {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Interactive Focus States */
  .focus-ring {
    transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .focus-ring:focus-visible {
    outline: none;
    box-shadow:
      0 0 0 2px rgba(59, 130, 246, 0.5),
      0 0 0 4px rgba(59, 130, 246, 0.2);
    transform: scale(1.02);
  }

  /* Enhanced Loading States */
  .loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .loading-pulse {
    animation: loading-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
  }

  /* Typography Enhancements */
  .text-balance {
    text-wrap: balance;
  }

  .letter-spacing-tight {
    letter-spacing: -0.02em;
  }

  .letter-spacing-wide {
    letter-spacing: 0.05em;
  }

  .text-shadow-glow {
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  }

  .text-gradient-animated {
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-12px); }
}

@keyframes glow {
  from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.2); }
  to { box-shadow: 0 0 25px rgba(59, 130, 246, 0.4); }
}

@keyframes glowSubtle {
  from { box-shadow: 0 0 10px rgba(59, 130, 246, 0.1); }
  to { box-shadow: 0 0 15px rgba(59, 130, 246, 0.2); }
}

@keyframes breathe {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.02); opacity: 1; }
}

@keyframes pulseSubtle {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.85; }
}

@keyframes drift {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  25% { transform: translateX(10px) translateY(-5px); }
  50% { transform: translateX(-5px) translateY(-10px); }
  75% { transform: translateX(-10px) translateY(5px); }
}

/* Cinematic Keyframes */
@keyframes sectionReveal {
  0% {
    opacity: 0;
    transform: translateY(80px) scale(0.95);
    filter: blur(10px);
  }
  50% {
    opacity: 0.7;
    transform: translateY(20px) scale(0.98);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
}

@keyframes heroEntrance {
  0% {
    opacity: 0;
    transform: translateY(100px) scale(0.9);
    filter: blur(20px);
  }
  30% {
    opacity: 0.3;
    transform: translateY(50px) scale(0.95);
    filter: blur(10px);
  }
  70% {
    opacity: 0.8;
    transform: translateY(10px) scale(0.99);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
}

@keyframes cardCascade {
  0% {
    opacity: 0;
    transform: translateY(60px) rotateX(15deg);
    filter: blur(5px);
  }
  60% {
    opacity: 0.8;
    transform: translateY(10px) rotateX(3deg);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) rotateX(0deg);
    filter: blur(0px);
  }
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95); /* Reduced distance and added scale */
    filter: blur(2px); /* Reduced blur for faster clear-up */
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
}

@keyframes progressiveBlur {
  0% {
    opacity: 0;
    filter: blur(8px) brightness(0.6); /* Reduced blur and increased brightness */
  }
  40% {
    opacity: 0.8; /* Faster opacity transition */
    filter: blur(3px) brightness(0.85); /* Faster blur clear-up */
  }
  100% {
    opacity: 1;
    filter: blur(0px) brightness(1);
  }
}

@keyframes parallaxSlow {
  0% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
  100% { transform: translateY(0px) rotate(360deg); }
}

@keyframes parallaxMedium {
  0% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-15px) translateX(10px); }
  50% { transform: translateY(-30px) translateX(0px); }
  75% { transform: translateY(-15px) translateX(-10px); }
  100% { transform: translateY(0px) translateX(0px); }
}

@keyframes parallaxFast {
  0% { transform: translateY(0px) scale(1); }
  33% { transform: translateY(-10px) scale(1.02); }
  66% { transform: translateY(-20px) scale(0.98); }
  100% { transform: translateY(0px) scale(1); }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98); /* Reduced distance and added scale */
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px); /* Less dramatic scale and added movement */
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced Cinematic Keyframes */
@keyframes cinematicReveal {
  0% {
    opacity: 0;
    transform: translateY(60px) scale(0.95) rotateX(8deg); /* Reduced distance and rotation */
    filter: blur(10px) brightness(0.5); /* Reduced blur for faster clear-up */
  }
  30% {
    opacity: 0.5; /* Faster opacity transition */
    transform: translateY(25px) scale(0.98) rotateX(3deg);
    filter: blur(4px) brightness(0.75); /* Faster blur clear-up */
  }
  70% {
    opacity: 0.9;
    transform: translateY(5px) scale(0.995) rotateX(1deg);
    filter: blur(1px) brightness(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1) rotateX(0deg);
    filter: blur(0px) brightness(1);
  }
}

@keyframes sequentialFade {
  0% {
    opacity: 0;
    transform: translateY(30px) translateX(-15px) scale(0.97); /* Reduced distances */
  }
  35% {
    opacity: 0.7; /* Faster opacity transition */
    transform: translateY(10px) translateX(-5px) scale(0.99);
  }
  70% {
    opacity: 0.95;
    transform: translateY(2px) translateX(-1px) scale(0.998);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) translateX(0px) scale(1);
  }
}

@keyframes parallaxFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) translateX(8px) rotate(1deg);
  }
  50% {
    transform: translateY(-25px) translateX(0px) rotate(0deg);
  }
  75% {
    transform: translateY(-15px) translateX(-8px) rotate(-1deg);
  }
}

@keyframes scaleBreath {
  0%, 100% {
    transform: scale(1) translateZ(0);
  }
  50% {
    transform: scale(1.03) translateZ(0);
  }
}

@keyframes textShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes elementChoreography {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.9) rotateY(8deg); /* Reduced distances and rotation */
    filter: blur(8px); /* Reduced blur for faster clear-up */
  }
  25% {
    opacity: 0.6; /* Faster opacity transition */
    transform: translateY(25px) scale(0.95) rotateY(4deg);
    filter: blur(4px); /* Faster blur clear-up */
  }
  60% {
    opacity: 0.9;
    transform: translateY(8px) scale(0.98) rotateY(1deg);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1) rotateY(0deg);
    filter: blur(0px);
  }
}

/* Enhanced Keyframes for Micro-Interactions */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.5),
      0 0 60px rgba(59, 130, 246, 0.2);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slide-in-up {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-down {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes zoom-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes flip-in {
  0% {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateY(-10deg);
  }
  70% {
    transform: perspective(400px) rotateY(10deg);
  }
  100% {
    transform: perspective(400px) rotateY(0deg);
    opacity: 1;
  }
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}