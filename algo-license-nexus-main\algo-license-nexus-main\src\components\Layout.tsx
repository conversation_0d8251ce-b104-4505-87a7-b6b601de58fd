import { ReactNode } from "react";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";

interface LayoutProps {
  children: ReactNode;
  className?: string;
}

/**
 * Modern Layout component providing consistent page structure
 * Features enhanced background gradients and proper spacing
 * Includes header navigation and footer across all pages
 */
export const Layout = ({ children, className = "" }: LayoutProps) => {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 relative ${className}`}>
      {/* Enhanced background effects */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900/50 via-blue-900/30 to-slate-800/50 pointer-events-none" />
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900/5 to-transparent pointer-events-none" />

      <Header />
      {/* Main content with top padding to account for fixed header */}
      <main className="pt-20 relative z-10">
        {children}
      </main>
      <Footer />
    </div>
  );
};
